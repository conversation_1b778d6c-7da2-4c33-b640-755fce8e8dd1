const { test, expect } = require('@playwright/test');
const { _electron: electron } = require('playwright');
const path = require('path');

// Skip tests if running in CI or if browsers aren't installed
const skipTests = process.env.CI || !process.env.PLAYWRIGHT_BROWSERS_PATH;

test.describe('FileDuck Electron App', () => {
  let electronApp;
  let page;

  test.beforeAll(async () => {
    test.skip(skipTests, 'Skipping Electron tests - browsers not installed');

    // Launch Electron app
    electronApp = await electron.launch({
      args: [path.join(__dirname, '..', 'src', 'main', 'main.js')],
      timeout: 30000,
    });

    // Get the first window that the app opens, wait if necessary
    page = await electronApp.firstWindow();

    // Wait for the app to be ready
    await page.waitForLoadState('domcontentloaded');
    await page.waitForTimeout(2000); // Give the app time to initialize
  });

  test.afterAll(async () => {
    if (electronApp) {
      await electronApp.close();
    }
  });

  test('should launch the application successfully', async () => {
    // Check if the window is visible
    expect(page).toBeTruthy();
    
    // Check if the title is correct
    const title = await page.title();
    expect(title).toBe('FileDuck');
  });

  test('should display the main application layout', async () => {
    // Wait for the main app container
    await page.waitForSelector('[data-testid="app-container"]', { timeout: 10000 });
    
    // Check if sidebar is present
    const sidebar = page.locator('[data-testid="sidebar"]');
    await expect(sidebar).toBeVisible();
    
    // Check if main content area is present
    const mainContent = page.locator('[data-testid="main-content"]');
    await expect(mainContent).toBeVisible();
  });

  test('should have working navigation tabs', async () => {
    // Check if Home tab is present and active by default
    const homeTab = page.locator('[data-testid="nav-home"]');
    await expect(homeTab).toBeVisible();
    
    // Check if Tool Store tab is present
    const storeTab = page.locator('[data-testid="nav-store"]');
    await expect(storeTab).toBeVisible();
    
    // Check if Preferences tab is present
    const preferencesTab = page.locator('[data-testid="nav-preferences"]');
    await expect(preferencesTab).toBeVisible();
  });

  test('should navigate between tabs correctly', async () => {
    // Click on Tool Store tab
    const storeTab = page.locator('[data-testid="nav-store"]');
    await storeTab.click();
    
    // Wait for store view to load
    await page.waitForTimeout(1000);
    
    // Check if store content is visible
    const storeContent = page.locator('text=Tool Store');
    await expect(storeContent).toBeVisible();
    
    // Click on Preferences tab
    const preferencesTab = page.locator('[data-testid="nav-preferences"]');
    await preferencesTab.click();
    
    // Wait for preferences view to load
    await page.waitForTimeout(1000);
    
    // Check if preferences content is visible
    const preferencesContent = page.locator('text=Preferences');
    await expect(preferencesContent).toBeVisible();
    
    // Go back to Home
    const homeTab = page.locator('[data-testid="nav-home"]');
    await homeTab.click();
    
    // Wait for home view to load
    await page.waitForTimeout(1000);
    
    // Check if home content is visible
    const homeContent = page.locator('text=Good morning');
    await expect(homeContent).toBeVisible();
  });

  test('should display installed apps in home view', async () => {
    // Make sure we're on home tab
    const homeTab = page.locator('[data-testid="nav-home"]');
    await homeTab.click();
    await page.waitForTimeout(1000);
    
    // Check if installed apps section exists
    const installedAppsSection = page.locator('text=Installed Apps');
    await expect(installedAppsSection).toBeVisible();
  });

  test('should have functional search in tool store', async () => {
    // Navigate to Tool Store
    const storeTab = page.locator('[data-testid="nav-store"]');
    await storeTab.click();
    await page.waitForTimeout(1000);
    
    // Find search input
    const searchInput = page.locator('input[placeholder="Search"]');
    await expect(searchInput).toBeVisible();
    
    // Type in search input
    await searchInput.fill('test');
    await page.waitForTimeout(500);
    
    // Clear search
    await searchInput.clear();
    await page.waitForTimeout(500);
  });

  test('should display app sections in tool store', async () => {
    // Navigate to Tool Store
    const storeTab = page.locator('[data-testid="nav-store"]');
    await storeTab.click();
    await page.waitForTimeout(1000);
    
    // Check for store sections
    const trendingSection = page.locator('text=See what\'s trending');
    await expect(trendingSection).toBeVisible();
    
    const popularSection = page.locator('text=Popular apps');
    await expect(popularSection).toBeVisible();
    
    const newSection = page.locator('text=New published');
    await expect(newSection).toBeVisible();
    
    const recommendedSection = page.locator('text=Recommended for you');
    await expect(recommendedSection).toBeVisible();
  });

  test('should have proper macOS styling applied', async () => {
    // Check if the app has the macOS background color
    const appContainer = page.locator('[data-testid="app-container"]');
    const backgroundColor = await appContainer.evaluate(el => 
      window.getComputedStyle(el).backgroundColor
    );
    
    // Should have a light background (macOS style)
    expect(backgroundColor).toBeTruthy();
    
    // Check if sidebar has backdrop blur effect
    const sidebar = page.locator('[data-testid="sidebar"]');
    const backdropFilter = await sidebar.evaluate(el => 
      window.getComputedStyle(el).backdropFilter
    );
    
    // Should have blur effect
    expect(backdropFilter).toContain('blur');
  });

  test('should handle window controls properly', async () => {
    // Check if the window is properly sized
    const windowSize = await page.viewportSize();
    expect(windowSize.width).toBeGreaterThan(800);
    expect(windowSize.height).toBeGreaterThan(600);
    
    // Check if the window is resizable (Electron specific)
    const isResizable = await electronApp.evaluate(async ({ app }) => {
      const windows = app.windows();
      return windows[0] ? windows[0].isResizable() : false;
    });
    expect(isResizable).toBe(true);
  });

  test('should not have console errors', async () => {
    const consoleErrors = [];
    
    page.on('console', msg => {
      if (msg.type() === 'error') {
        consoleErrors.push(msg.text());
      }
    });
    
    // Navigate through the app
    const storeTab = page.locator('[data-testid="nav-store"]');
    await storeTab.click();
    await page.waitForTimeout(1000);
    
    const preferencesTab = page.locator('[data-testid="nav-preferences"]');
    await preferencesTab.click();
    await page.waitForTimeout(1000);
    
    const homeTab = page.locator('[data-testid="nav-home"]');
    await homeTab.click();
    await page.waitForTimeout(1000);
    
    // Filter out known extension errors that are not related to our app
    const appErrors = consoleErrors.filter(error => 
      !error.includes('Extension Error') && 
      !error.includes('chrome-extension') &&
      !error.includes('ExecutionWorld')
    );
    
    expect(appErrors).toHaveLength(0);
  });
});
