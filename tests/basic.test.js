const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

describe('FileDuck Basic Tests', () => {
  let serverProcess;
  let electronProcess;

  beforeAll(() => {
    // Change to project directory
    process.chdir(path.join(__dirname, '..'));
  });

  afterAll(() => {
    // Clean up processes
    if (serverProcess) {
      serverProcess.kill();
    }
    if (electronProcess) {
      electronProcess.kill();
    }
  });

  test('should have all required files', () => {
    // Check if main files exist
    expect(fs.existsSync('package.json')).toBe(true);
    expect(fs.existsSync('src/App.jsx')).toBe(true);
    expect(fs.existsSync('src/components/Sidebar.jsx')).toBe(true);
    expect(fs.existsSync('src/components/HomeView.jsx')).toBe(true);
    expect(fs.existsSync('src/components/StoreView.jsx')).toBe(true);
    expect(fs.existsSync('src/components/PreferencesView.jsx')).toBe(true);
    expect(fs.existsSync('src/main/main.js')).toBe(true);
  });

  test('should have correct package.json structure', () => {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    
    expect(packageJson.name).toBe('fileduck-desktop');
    expect(packageJson.main).toBe('build/electron.js');
    expect(packageJson.scripts).toHaveProperty('start');
    expect(packageJson.scripts).toHaveProperty('build');
    expect(packageJson.scripts).toHaveProperty('test:e2e');
    
    // Check if required dependencies exist
    expect(packageJson.dependencies).toHaveProperty('react');
    expect(packageJson.dependencies).toHaveProperty('electron-is-dev');
    expect(packageJson.dependencies).toHaveProperty('framer-motion');
    expect(packageJson.devDependencies).toHaveProperty('@playwright/test');
  });

  test('should compile React app successfully', (done) => {
    const buildProcess = spawn('npm', ['run', 'build:renderer'], {
      stdio: 'pipe',
      shell: true
    });

    let output = '';
    let errorOutput = '';

    buildProcess.stdout.on('data', (data) => {
      output += data.toString();
    });

    buildProcess.stderr.on('data', (data) => {
      errorOutput += data.toString();
    });

    buildProcess.on('close', (code) => {
      console.log('Build output:', output);
      if (errorOutput) {
        console.log('Build errors:', errorOutput);
      }
      
      expect(code).toBe(0);
      expect(fs.existsSync('build')).toBe(true);
      expect(fs.existsSync('build/index.html')).toBe(true);
      done();
    });

    // Set timeout for build process
    setTimeout(() => {
      buildProcess.kill();
      done(new Error('Build process timed out'));
    }, 120000); // 2 minutes timeout
  }, 150000); // 2.5 minutes Jest timeout

  test('should start development server without errors', (done) => {
    serverProcess = spawn('npm', ['run', 'start:renderer'], {
      stdio: 'pipe',
      shell: true,
      env: { ...process.env, BROWSER: 'none' }
    });

    let output = '';
    let hasStarted = false;

    serverProcess.stdout.on('data', (data) => {
      output += data.toString();
      
      // Check if server started successfully
      if (output.includes('webpack compiled') && output.includes('successfully')) {
        hasStarted = true;
        setTimeout(() => {
          expect(hasStarted).toBe(true);
          expect(output).toContain('Local:');
          expect(output).toContain('3000');
          done();
        }, 2000); // Wait 2 seconds after successful compilation
      }
    });

    serverProcess.stderr.on('data', (data) => {
      const errorData = data.toString();
      // Only fail on actual compilation errors, not warnings
      if (errorData.includes('Failed to compile') || errorData.includes('Module build failed')) {
        done(new Error(`Development server failed: ${errorData}`));
      }
    });

    serverProcess.on('close', (code) => {
      if (!hasStarted && code !== 0) {
        done(new Error(`Development server exited with code ${code}`));
      }
    });

    // Set timeout for server startup
    setTimeout(() => {
      if (!hasStarted) {
        done(new Error('Development server failed to start within timeout'));
      }
    }, 60000); // 1 minute timeout
  }, 90000); // 1.5 minutes Jest timeout

  test('should have proper macOS styling classes', () => {
    const appContent = fs.readFileSync('src/App.jsx', 'utf8');
    const sidebarContent = fs.readFileSync('src/components/Sidebar.jsx', 'utf8');
    
    // Check for macOS-specific classes
    expect(appContent).toContain('bg-macos-bg');
    expect(appContent).toContain('font-system');
    expect(sidebarContent).toContain('backdrop-blur');
    expect(sidebarContent).toContain('bg-macos-surface');
  });

  test('should have test IDs for automation', () => {
    const appContent = fs.readFileSync('src/App.jsx', 'utf8');
    const sidebarContent = fs.readFileSync('src/components/Sidebar.jsx', 'utf8');
    
    // Check for test IDs
    expect(appContent).toContain('data-testid="app-container"');
    expect(appContent).toContain('data-testid="main-content"');
    expect(sidebarContent).toContain('data-testid="nav-home"');
    expect(sidebarContent).toContain('data-testid="nav-store"');
    expect(sidebarContent).toContain('data-testid="nav-preferences"');
  });

  test('should have proper component structure', () => {
    const storeViewContent = fs.readFileSync('src/components/StoreView.jsx', 'utf8');
    const homeViewContent = fs.readFileSync('src/components/HomeView.jsx', 'utf8');
    
    // Check for proper JSX structure (no syntax errors)
    expect(storeViewContent).toContain('export default StoreView');
    expect(homeViewContent).toContain('export default HomeView');
    
    // Check for proper imports
    expect(storeViewContent).toContain('import React');
    expect(homeViewContent).toContain('import React');
  });
});
