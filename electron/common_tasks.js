const path = require('path');
const {app} = require('electron');
const fs = require('fs');


function createFolder(folderPath = "", isRandom = false) {
    const userDataPath = app.getPath('userData');
    if (isRandom) {
        folderPath = folderPath + '_' + Date.now();
    }
    folderPath = path.join(userDataPath, folderPath);
    if (fs.existsSync(folderPath)) {
        fs.rmSync(folderPath, {recursive: true, force: true});
    }
    fs.mkdirSync(folderPath, {recursive: true});
    return folderPath;
}

module.exports = {
    createFolder
}
