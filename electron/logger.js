const { Signale } = require('signale');

// Enhanced logger configuration with Gen-Z emojis and colorful logs
const options = {
  displayScope: true,
  displayBadge: true,
  displayDate: false,
  displayFilename: false,
  displayLabel: true,
  displayTimestamp: true,
  underlineLabel: false,
  underlineMessage: false,
  underlinePrefix: false,
  underlineSuffix: false,
  uppercaseLabel: false,
  types: {
    // Success and positive actions with Gen-Z vibes ✨
    success: {
      badge: '✅',
      color: 'green',
      label: 'success',
      logLevel: 'info'
    },
    complete: {
      badge: '🎉',
      color: 'green',
      label: 'complete',
      logLevel: 'info'
    },
    slay: {
      badge: '💅',
      color: 'magenta',
      label: 'slay',
      logLevel: 'info'
    },
    // Information and status - no cap 📠
    info: {
      badge: 'ℹ️',
      color: 'blue',
      label: 'info',
      logLevel: 'info'
    },
    note: {
      badge: '📝',
      color: 'blue',
      label: 'note',
      logLevel: 'info'
    },
    facts: {
      badge: '📠',
      color: 'cyan',
      label: 'facts',
      logLevel: 'info'
    },
    // Process and loading states - it's giving productivity 💪
    pending: {
      badge: '⏳',
      color: 'yellow',
      label: 'pending',
      logLevel: 'info'
    },
    await: {
      badge: '⌛',
      color: 'yellow',
      label: 'await',
      logLevel: 'info'
    },
    watch: {
      badge: '👀',
      color: 'yellow',
      label: 'watch',
      logLevel: 'info'
    },
    start: {
      badge: '🚀',
      color: 'cyan',
      label: 'start',
      logLevel: 'info'
    },
    vibe: {
      badge: '✨',
      color: 'magenta',
      label: 'vibe',
      logLevel: 'info'
    },
    // Warnings and cautions - sus behavior 🤔
    warn: {
      badge: '⚠️',
      color: 'yellow',
      label: 'warn',
      logLevel: 'warn'
    },
    sus: {
      badge: '🤔',
      color: 'yellow',
      label: 'sus',
      logLevel: 'warn'
    },
    pause: {
      badge: '⏸️',
      color: 'yellow',
      label: 'pause',
      logLevel: 'warn'
    },
    // Errors and failures - not it chief 💀
    error: {
      badge: '❌',
      color: 'red',
      label: 'error',
      logLevel: 'error'
    },
    fatal: {
      badge: '💀',
      color: 'red',
      label: 'FATAL',
      logLevel: 'error'
    },
    rip: {
      badge: '⚰️',
      color: 'red',
      label: 'rip',
      logLevel: 'error'
    },
    // Debug and development - touch grass mode 🌱
    debug: {
      badge: '🐛',
      color: 'magenta',
      label: 'debug',
      logLevel: 'debug'
    },
    dev: {
      badge: '🧑‍💻',
      color: 'cyan',
      label: 'dev',
      logLevel: 'debug'
    },
    // Special FileDuck specific loggers - main character energy 💫
    fileduck: {
      badge: '🦆',
      color: 'cyan',
      label: 'fileduck',
      logLevel: 'info'
    },
    app: {
      badge: '📱',
      color: 'blue',
      label: 'app',
      logLevel: 'info'
    },
    install: {
      badge: '📦',
      color: 'green',
      label: 'install',
      logLevel: 'info'
    },
    uninstall: {
      badge: '🗑️',
      color: 'red',
      label: 'uninstall',
      logLevel: 'info'
    },
    python: {
      badge: '🐍',
      color: 'yellow',
      label: 'python',
      logLevel: 'info'
    },
    file: {
      badge: '📄',
      color: 'blue',
      label: 'file',
      logLevel: 'info'
    },
    api: {
      badge: '🌐',
      color: 'cyan',
      label: 'api',
      logLevel: 'info'
    },
    database: {
      badge: '🗄️',
      color: 'magenta',
      label: 'database',
      logLevel: 'info'
    },
    electron: {
      badge: '⚡',
      color: 'blue',
      label: 'electron',
      logLevel: 'info'
    },
    ipc: {
      badge: '🔗',
      color: 'cyan',
      label: 'ipc',
      logLevel: 'info'
    },
    // Extra Gen-Z vibes 🔥
    fire: {
      badge: '🔥',
      color: 'red',
      label: 'fire',
      logLevel: 'info'
    },
    bussin: {
      badge: '💯',
      color: 'green',
      label: 'bussin',
      logLevel: 'info'
    },
    periodt: {
      badge: '💅',
      color: 'magenta',
      label: 'periodt',
      logLevel: 'info'
    }
  }
};

// Create the main logger instance
const logger = new Signale(options);

// CommonJS exports
module.exports = {
  logger,
  Signale,
  createLogger: (scope) => logger.scope(scope)
};
