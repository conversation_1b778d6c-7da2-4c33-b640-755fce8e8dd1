#!/usr/bin/env node

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 FileDuck Testing Suite');
console.log('========================\n');

let hasErrors = false;

function runCommand(command, args, description) {
  return new Promise((resolve, reject) => {
    console.log(`📋 ${description}...`);
    
    const process = spawn(command, args, {
      stdio: 'pipe',
      shell: true
    });

    let output = '';
    let errorOutput = '';

    process.stdout.on('data', (data) => {
      output += data.toString();
    });

    process.stderr.on('data', (data) => {
      errorOutput += data.toString();
    });

    process.on('close', (code) => {
      if (code === 0) {
        console.log(`✅ ${description} - PASSED\n`);
        resolve({ success: true, output, errorOutput });
      } else {
        console.log(`❌ ${description} - FAILED`);
        console.log(`Exit code: ${code}`);
        if (errorOutput) {
          console.log(`Error: ${errorOutput.substring(0, 500)}...\n`);
        }
        hasErrors = true;
        resolve({ success: false, output, errorOutput, code });
      }
    });

    process.on('error', (error) => {
      console.log(`❌ ${description} - ERROR: ${error.message}\n`);
      hasErrors = true;
      reject(error);
    });
  });
}

async function checkFileStructure() {
  console.log('📁 Checking file structure...');
  
  const requiredFiles = [
    'package.json',
    'src/App.jsx',
    'src/components/Sidebar.jsx',
    'src/components/HomeView.jsx',
    'src/components/StoreView.jsx',
    'src/components/PreferencesView.jsx',
    'src/main/main.js',
    'playwright.config.js',
    'tests/basic.test.js',
    'tests/electron.spec.js'
  ];

  let allFilesExist = true;
  
  for (const file of requiredFiles) {
    if (!fs.existsSync(file)) {
      console.log(`❌ Missing file: ${file}`);
      allFilesExist = false;
      hasErrors = true;
    }
  }

  if (allFilesExist) {
    console.log('✅ File structure check - PASSED\n');
  } else {
    console.log('❌ File structure check - FAILED\n');
  }

  return allFilesExist;
}

async function checkSyntax() {
  console.log('🔍 Checking JSX syntax...');
  
  const jsxFiles = [
    'src/App.jsx',
    'src/components/Sidebar.jsx',
    'src/components/HomeView.jsx',
    'src/components/StoreView.jsx',
    'src/components/PreferencesView.jsx'
  ];

  let syntaxValid = true;

  for (const file of jsxFiles) {
    try {
      const content = fs.readFileSync(file, 'utf8');
      
      // Basic syntax checks
      const openBraces = (content.match(/{/g) || []).length;
      const closeBraces = (content.match(/}/g) || []).length;
      const openParens = (content.match(/\(/g) || []).length;
      const closeParens = (content.match(/\)/g) || []).length;
      
      if (openBraces !== closeBraces) {
        console.log(`❌ ${file}: Mismatched braces (${openBraces} open, ${closeBraces} close)`);
        syntaxValid = false;
        hasErrors = true;
      }
      
      if (openParens !== closeParens) {
        console.log(`❌ ${file}: Mismatched parentheses (${openParens} open, ${closeParens} close)`);
        syntaxValid = false;
        hasErrors = true;
      }

      // Check for export statement
      if (!content.includes('export default')) {
        console.log(`❌ ${file}: Missing export default statement`);
        syntaxValid = false;
        hasErrors = true;
      }

    } catch (error) {
      console.log(`❌ ${file}: Error reading file - ${error.message}`);
      syntaxValid = false;
      hasErrors = true;
    }
  }

  if (syntaxValid) {
    console.log('✅ JSX syntax check - PASSED\n');
  } else {
    console.log('❌ JSX syntax check - FAILED\n');
  }

  return syntaxValid;
}

async function runTests() {
  console.log('🧪 Running test suite...\n');

  // Check file structure
  await checkFileStructure();

  // Check syntax
  await checkSyntax();

  // Run basic tests (excluding long-running ones)
  await runCommand('npm', ['run', 'test:basic', '--', '--testNamePattern=should have all required files|should have correct package.json structure|should have proper macOS styling classes|should have test IDs for automation|should have proper component structure'], 'Basic functionality tests');

  // Try to build the project
  await runCommand('npm', ['run', 'build:renderer'], 'React build compilation');

  // Check if development server can start (with timeout)
  console.log('🌐 Testing development server startup...');
  const serverTest = spawn('npm', ['run', 'start:renderer'], {
    stdio: 'pipe',
    shell: true,
    env: { ...process.env, BROWSER: 'none' }
  });

  let serverStarted = false;
  let serverOutput = '';

  const serverPromise = new Promise((resolve) => {
    serverTest.stdout.on('data', (data) => {
      serverOutput += data.toString();
      if (serverOutput.includes('webpack compiled') && serverOutput.includes('successfully')) {
        serverStarted = true;
        serverTest.kill();
        resolve(true);
      }
    });

    serverTest.stderr.on('data', (data) => {
      const errorData = data.toString();
      if (errorData.includes('Failed to compile') || errorData.includes('Module build failed')) {
        serverTest.kill();
        resolve(false);
      }
    });

    // Timeout after 30 seconds
    setTimeout(() => {
      serverTest.kill();
      resolve(serverStarted);
    }, 30000);
  });

  const serverResult = await serverPromise;
  
  if (serverResult) {
    console.log('✅ Development server startup - PASSED\n');
  } else {
    console.log('❌ Development server startup - FAILED\n');
    hasErrors = true;
  }

  // Summary
  console.log('📊 Test Summary');
  console.log('===============');
  
  if (hasErrors) {
    console.log('❌ Some tests failed. Please review the errors above.');
    console.log('\n🔧 Common fixes:');
    console.log('   - Check for syntax errors in JSX files');
    console.log('   - Ensure all required dependencies are installed');
    console.log('   - Run "npm install" if packages are missing');
    process.exit(1);
  } else {
    console.log('✅ All tests passed! Your FileDuck application is ready.');
    console.log('\n🚀 To start the application:');
    console.log('   npm start');
    console.log('\n🧪 To run specific tests:');
    console.log('   npm run test:basic    # Basic functionality tests');
    console.log('   npm run test:e2e      # End-to-end tests (requires Playwright setup)');
  }
}

// Run the tests
runTests().catch((error) => {
  console.error('❌ Test runner failed:', error.message);
  process.exit(1);
});
