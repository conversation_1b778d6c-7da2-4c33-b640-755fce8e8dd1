/** @type {import('tailwindcss').Config} */
module.exports = {
  darkMode: ["class"],
  content: [
    './pages/**/*.{js,jsx}',
    './components/**/*.{js,jsx}',
    './app/**/*.{js,jsx}',
    './src/**/*.{js,jsx}',
  ],
  prefix: "",
  theme: {
  	container: {
  		center: true,
  		padding: '2rem',
  		screens: {
  			'2xl': '1400px'
  		}
  	},
  	extend: {
  		fontFamily: {
  			sans: [
  				'Rubik',
  				'sans-serif'
  			],
  			system: [
  				'-apple-system',
  				'BlinkMacSystemFont',
  				'San Francisco',
  				'Helvetica Neue',
  				'Arial',
  				'sans-serif'
  			]
  		},
  		fontSize: {
  			xs: [
  				'11px',
  				'16px'
  			],
  			sm: [
  				'12px',
  				'18px'
  			],
  			base: [
  				'13px',
  				'20px'
  			],
  			lg: [
  				'14px',
  				'22px'
  			],
  			xl: [
  				'16px',
  				'24px'
  			],
  			'2xl': [
  				'20px',
  				'28px'
  			],
  			'3xl': [
  				'24px',
  				'32px'
  			]
  		},
  		colors: {
  			macos: {
  				bg: '#f5f5f7',
  				surface: '#ffffff',
  				elevated: '#fbfbfb',
  				border: '#e5e5e7',
  				divider: '#d2d2d7',
  				text: {
  					primary: '#1d1d1f',
  					secondary: '#86868b',
  					tertiary: '#afafaf'
  				}
  			},
  			system: {
  				blue: '#007aff',
  				green: '#34c759',
  				orange: '#ff9500',
  				red: '#ff3b30',
  				purple: '#af52de',
  				pink: '#ff2d92'
  			},
  			border: 'hsl(var(--border))',
  			input: 'hsl(var(--input))',
  			ring: 'hsl(var(--ring))',
  			background: 'hsl(var(--background))',
  			foreground: 'hsl(var(--foreground))',
  			primary: {
  				DEFAULT: '#FF6D4A',
  				foreground: 'hsl(var(--primary-foreground))'
  			},
  			secondary: {
  				DEFAULT: 'hsl(var(--secondary))',
  				foreground: 'hsl(var(--secondary-foreground))'
  			},
  			destructive: {
  				DEFAULT: 'hsl(var(--destructive))',
  				foreground: 'hsl(var(--destructive-foreground))'
  			},
  			muted: {
  				DEFAULT: 'hsl(var(--muted))',
  				foreground: 'hsl(var(--muted-foreground))'
  			},
  			accent: {
  				DEFAULT: 'hsl(var(--accent))',
  				foreground: 'hsl(var(--accent-foreground))'
  			},
  			popover: {
  				DEFAULT: 'hsl(var(--popover))',
  				foreground: 'hsl(var(--popover-foreground))'
  			},
  			card: {
  				DEFAULT: 'hsl(var(--card))',
  				foreground: 'hsl(var(--card-foreground))'
  			},
  			fileduck: {
  				'bg-main': '#FDFDFD',
  				'bg-sidebar': '#F7F7F8',
  				primary: '#FF6D4A',
  				'text-primary': '#1F2937',
  				'text-secondary': '#9CA3AF'
  			},
  			'tool-card': {
  				purple: '#8B5CF6',
  				blue: '#3B82F6',
  				green: '#10B981',
  				pink: '#EC4899',
  				indigo: '#6366F1',
  				teal: '#14B8A6'
  			}
  		},
  		borderRadius: {
  			lg: 'var(--radius)',
  			md: 'calc(var(--radius) - 2px)',
  			sm: 'calc(var(--radius) - 4px)'
  		},
  		keyframes: {
  			'accordion-down': {
  				from: {
  					height: '0'
  				},
  				to: {
  					height: 'var(--radix-accordion-content-height)'
  				}
  			},
  			'accordion-up': {
  				from: {
  					height: 'var(--radix-accordion-content-height)'
  				},
  				to: {
  					height: '0'
  				}
  			},
  			'accordion-down': {
  				from: {
  					height: '0'
  				},
  				to: {
  					height: 'var(--radix-accordion-content-height)'
  				}
  			},
  			'accordion-up': {
  				from: {
  					height: 'var(--radix-accordion-content-height)'
  				},
  				to: {
  					height: '0'
  				}
  			}
  		},
  		animation: {
  			'accordion-down': 'accordion-down 0.2s ease-out',
  			'accordion-up': 'accordion-up 0.2s ease-out',
  			'accordion-down': 'accordion-down 0.2s ease-out',
  			'accordion-up': 'accordion-up 0.2s ease-out'
  		}
  	}
  },
  plugins: [require("tailwindcss-animate")],
}
