{"name": "fileduck-desktop", "version": "0.1.0", "description": "FileDuck - Desktop utility application", "author": "FileDuck Team", "main": "build/electron.js", "homepage": "./", "scripts": {"start": "concurrently \"npm run start:renderer\" \"wait-on http://localhost:3000 && npm run start:electron\"", "start:clean": "node start-clean.js", "start:safe": "node start-safe.js", "health-check": "node test-app-health.js", "start:renderer": "BROWSER=none react-app-rewired start", "start:electron": "electron .", "build": "npm run build:renderer && npm run build:main && npm run build:electron", "build:renderer": "react-app-rewired build", "build:main": "cp -r src/main/* build/ && mv build/main.js build/electron.js", "build:electron": "electron-builder", "build:dmg": "npm run build && electron-builder --mac --publish=never", "dist": "npm run build && electron-builder --publish=never", "test": "react-app-rewired test", "test:basic": "jest tests/basic.test.js", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:debug": "playwright test --debug"}, "dependencies": {"@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-tooltip": "^1.2.7", "@tabler/icons-react": "^3.34.0", "adm-zip": "^0.5.16", "better-sqlite3": "^11.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "electron-is-dev": "^3.0.1", "electron-store": "^10.1.0", "framer-motion": "^10.0.0", "lodash": "^4.17.21", "lucide-react": "^0.263.1", "pyodide": "^0.27.7", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hotkeys-hook": "^5.1.0", "react-router-dom": "^6.8.0", "react-scripts": "5.0.1", "signale": "^1.4.0", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "zod": "^3.25.67"}, "devDependencies": {"@playwright/test": "^1.53.1", "@tailwindcss/forms": "^0.5.3", "assert": "^2.1.0", "autoprefixer": "^10.4.13", "chalk": "4.1.2", "concurrently": "^7.6.0", "constants-browserify": "^1.0.0", "electron": "22.0.0", "electron-builder": "^24.0.0", "electron-devtools-installer": "^4.0.0", "electron-rebuild": "^3.2.9", "jest": "^27.5.1", "path-browserify": "^1.0.1", "playwright": "^1.53.1", "postcss": "^8.4.21", "prettier": "^3.5.3", "react-app-rewired": "^2.2.1", "stream-browserify": "^3.0.0", "tailwindcss": "^3.2.0", "util": "^0.12.5", "wait-on": "^7.0.1"}, "jest": {"testEnvironment": "node", "testTimeout": 180000, "setupFilesAfterEnv": [], "testMatch": ["**/tests/**/*.test.js"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "build": {"appId": "com.fileduck.desktop", "productName": "FileDuck", "directories": {"output": "dist"}, "files": ["build/**/*", "src/main/**/*", "node_modules/**/*", "public/duck-icon.png", "!node_modules/**/{README.md,readme.md,CHANGELOG.md,changelog.md}", "!node_modules/**/{test,tests,spec,specs}/**/*", "!node_modules/**/*.{md,txt,map}", "!node_modules/**/.*"], "extraResources": [{"from": "public/duck-icon.png", "to": "duck-icon.png"}], "mac": {"category": "public.app-category.productivity", "icon": "public/duck-icon.png", "target": [{"target": "dmg", "arch": ["x64", "arm64"]}], "bundleVersion": "1.0.0", "minimumSystemVersion": "10.14.0", "hardenedRuntime": true, "gatekeeperAssess": false, "entitlements": "build/entitlements.mac.plist", "entitlementsInherit": "build/entitlements.mac.plist"}, "dmg": {"title": "FileDuck Installer", "icon": "public/duck-icon.png", "contents": [{"x": 130, "y": 220}, {"x": 410, "y": 220, "type": "link", "path": "/Applications"}], "window": {"width": 540, "height": 380}, "artifactName": "FileDuck-${version}-${arch}.${ext}"}, "win": {"target": "nsis", "icon": "public/duck-icon.png"}, "linux": {"target": "AppImage", "icon": "public/duck-icon.png"}}}