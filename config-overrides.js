const path = require('path');

module.exports = function override(config, env) {
  // Add fallbacks for Node.js core modules
  config.resolve.fallback = {
    ...config.resolve.fallback,
    "util": require.resolve("util/"),
    "path": require.resolve("path-browserify"),
    "stream": require.resolve("stream-browserify"),
    "assert": require.resolve("assert/"),
    "constants": require.resolve("constants-browserify"),
    "fs": false,
    "readline": false,
  };

  // Ignore Node.js-specific modules that should only run in main process
  config.externals = {
    ...config.externals,
    'better-sqlite3': 'commonjs better-sqlite3',
    'electron': 'commonjs electron',
  };

  return config;
};
