import React, { useState } from 'react';
import { Button } from './ui/button';
import { Bug } from 'lucide-react';
import { logger } from '../utils/logger';

const DebugToggle = () => {
  const [isOpen, setIsOpen] = useState(false);

  const handleToggleDebug = async () => {
    try {
      if (window.electronAPI && window.electronAPI.toggleDevTools) {
        await window.electronAPI.toggleDevTools();
        setIsOpen(!isOpen);
      } else {
        // Fallback for web browser (though app should be desktop-only)
        logger.info('Debug toggle only available in desktop app');
      }
    } catch (error) {
      logger.error('Failed to toggle debug console:', error);
    }
  };

  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={handleToggleDebug}
      className="fixed top-4 right-4 z-50 w-8 h-8 p-0 rounded-full bg-gray-100 hover:bg-gray-200 shadow-sm"
      title="Toggle Debug Console"
    >
      <Bug className="h-4 w-4 text-gray-600" />
    </Button>
  );
};

export default DebugToggle;
