import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Button } from './ui/button';
import { Card, CardContent } from './ui/card';
import { ArrowLeft, Upload, Play } from 'lucide-react';
import { motion } from 'framer-motion';
// Legacy WASM app runner removed
import BlueprintRenderer from './BlueprintRenderer';
import { logger } from '../utils/logger';

const UtilityRunnerView = () => {
  const { utilityId } = useParams();
  const navigate = useNavigate();
  const [dragOver, setDragOver] = useState(false);
  const [files, setFiles] = useState([]);
  const [appConfig, setAppConfig] = useState(null);
  const [isWasmApp, setIsWasmApp] = useState(false);

  // Load utility configuration using the new API
  useEffect(() => {
    const loadUtilityConfig = async () => {
      try {
        if (window.electronAPI) {
          // Get installed utilities to find the one we need
          const utilities = await window.electronAPI.getInstalledUtilities();
          const utility = utilities.find(u => u.id === utilityId);

          if (utility) {
            logger.debug('Found utility:', utility);
            // Run utility to get its configuration
            const utilityData = await window.electronAPI.runUtility(utilityId, [], {});
            logger.debug('Utility data received:', utilityData);
            logger.debug('Utility config:', utilityData.config);
            setAppConfig(utilityData.config);
            setIsWasmApp(true);
          } else {
            logger.warn('Utility not found:', utilityId);
            setIsWasmApp(false);
          }
        } else {
          setIsWasmApp(false);
        }
      } catch (error) {
        logger.error('Failed to load utility config:', error);
        setIsWasmApp(false);
      }
    };

    loadUtilityConfig();
  }, [utilityId]);

  // Utility names mapping for legacy apps
  const utilityNames = {
    'csv-to-json': 'CSV to JSON Converter',
    'resize-image': 'Image Resizer',
    'batch-rename': 'Batch File Renamer',
    'pdf-merger': 'PDF Merger',
    'video-converter': 'Video Converter',
    'audio-extractor': 'Audio Extractor'
  };

  const utilityName = appConfig?.name || utilityNames[utilityId] || 'Unknown Utility';

  // Handle different app types
  if (isWasmApp && appConfig) {
    // Check if this is the new config format (no UI blueprints)
    const isNewFormat = !appConfig.ui && appConfig.language;

    if (isNewFormat) {
      // Handle specific apps with custom UI
      if (utilityId.includes('json-to-csv')) {
        return (
          <div className="p-8 h-full overflow-y-auto">
            <div className="max-w-7xl mx-auto">
              {/* Header */}
              <div className="flex items-center space-x-4 mb-8">
                <Button
                  variant="ghost"
                  onClick={() => navigate('/')}
                  className="text-fileduck-text-secondary hover:text-fileduck-text-primary"
                >
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Back to Home
                </Button>
              </div>

              {/* App Header */}
              <div className="flex items-center gap-4 mb-8">
                <div className="w-16 h-16 bg-fileduck-primary rounded-xl flex items-center justify-center">
                  <span className="text-2xl">📊</span>
                </div>
                <div>
                  <h1 className="text-3xl font-bold text-fileduck-text-primary">
                    {appConfig.name}
                  </h1>
                  <p className="text-fileduck-text-secondary">
                    {appConfig.description}
                  </p>
                </div>
              </div>

              {/* Dynamic UI Renderer */}
              <BlueprintRenderer
                config={appConfig}
                utilityId={utilityId}
                onExecute={async (fileData, config) => {
                  try {
                    const result = await window.electronAPI.runUtility(utilityId, fileData, config);
                    return result;
                  } catch (error) {
                    throw error;
                  }
                }}
              />
            </div>
          </div>
        );
      }

      // For other new format apps, show a simple message
      return (
        <div className="p-8 h-full overflow-y-auto">
          <div className="max-w-4xl mx-auto">
            <div className="flex items-center space-x-4 mb-8">
              <Button
                variant="ghost"
                onClick={() => navigate('/')}
                className="text-fileduck-text-secondary hover:text-fileduck-text-primary"
              >
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Home
              </Button>
            </div>
            <h1 className="text-3xl font-bold text-fileduck-text-primary mb-4">
              {appConfig.name}
            </h1>
            <p className="text-fileduck-text-secondary mb-8">
              {appConfig.description}
            </p>
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
              <p className="text-blue-800">
                This utility uses the new simplified format. Custom UI coming soon!
              </p>
            </div>
          </div>
        </div>
      );
    } else {
      // Legacy format no longer supported
      return (
        <div className="p-8 h-full overflow-y-auto">
          <div className="max-w-4xl mx-auto">
            <div className="flex items-center space-x-4 mb-8">
              <Button
                variant="ghost"
                onClick={() => navigate('/')}
                className="text-fileduck-text-secondary hover:text-fileduck-text-primary"
              >
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Home
              </Button>
            </div>
            <h1 className="text-3xl font-bold text-fileduck-text-primary mb-4">
              {appConfig.name}
            </h1>
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
              <p className="text-yellow-800">
                This utility uses the legacy format which is no longer supported.
                Please update to the new config.json format.
              </p>
            </div>
          </div>
        </div>
      );
    }
  }

  const handleDragOver = (e) => {
    e.preventDefault();
    setDragOver(true);
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    setDragOver(false);
  };

  const handleDrop = (e) => {
    e.preventDefault();
    setDragOver(false);
    
    const droppedFiles = Array.from(e.dataTransfer.files);
    setFiles(droppedFiles);
  };

  const handleFileSelect = (e) => {
    const selectedFiles = Array.from(e.target.files);
    setFiles(selectedFiles);
  };

  const handleRun = () => {
    if (files.length === 0) {
      alert('Please select files first');
      return;
    }
    
    // Simulate processing
    logger.info(`Running ${utilityName} on files:`, files);
    alert(`Processing ${files.length} file(s) with ${utilityName}...`);
  };

  return (
    <div className="p-8 h-full overflow-y-auto">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="flex items-center space-x-4 mb-8">
          <Button
            variant="ghost"
            onClick={() => navigate('/')}
            className="text-fileduck-text-secondary hover:text-fileduck-text-primary"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Home
          </Button>
        </div>

        <h1 className="text-3xl font-bold text-fileduck-text-primary mb-8">
          {utilityName}
        </h1>

        {/* Drag and Drop Zone */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <Card 
            className={`mb-6 transition-all duration-200 ${
              dragOver 
                ? 'border-fileduck-primary border-2 bg-fileduck-primary/5' 
                : 'border-dashed border-2 border-gray-300 hover:border-fileduck-primary/50'
            }`}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
          >
            <CardContent className="p-12 text-center">
              <div className="flex flex-col items-center space-y-4">
                <div className="w-16 h-16 bg-fileduck-primary/10 rounded-full flex items-center justify-center">
                  <Upload className="h-8 w-8 text-fileduck-primary" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-fileduck-text-primary mb-2">
                    Drop files here or click to browse
                  </h3>
                  <p className="text-fileduck-text-secondary mb-4">
                    Select the files you want to process with {utilityName}
                  </p>
                  <input
                    type="file"
                    multiple
                    onChange={handleFileSelect}
                    className="hidden"
                    id="file-input"
                  />
                  <Button
                    variant="outline"
                    onClick={() => document.getElementById('file-input').click()}
                    className="border-fileduck-primary text-fileduck-primary hover:bg-fileduck-primary hover:text-white"
                  >
                    Browse Files
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Selected Files */}
        {files.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.1 }}
          >
            <Card className="mb-6 bg-white">
              <CardContent className="p-6">
                <h3 className="text-lg font-semibold text-fileduck-text-primary mb-4">
                  Selected Files ({files.length})
                </h3>
                <div className="space-y-2 max-h-40 overflow-y-auto">
                  {files.map((file, index) => (
                    <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                      <span className="text-sm text-fileduck-text-primary truncate">
                        {file.name}
                      </span>
                      <span className="text-xs text-fileduck-text-secondary">
                        {(file.size / 1024).toFixed(1)} KB
                      </span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}

        {/* Utility-specific configuration placeholder */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.2 }}
        >
          <Card className="mb-6 bg-white">
            <CardContent className="p-6">
              <h3 className="text-lg font-semibold text-fileduck-text-primary mb-4">
                Configuration
              </h3>
              <div className="text-fileduck-text-secondary">
                {/* Utility-specific configuration options will be implemented here */}
                <p className="italic">Configuration options for {utilityName} will appear here.</p>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Run Button */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.3 }}
        >
          <Button
            onClick={handleRun}
            className="w-full bg-fileduck-primary hover:bg-fileduck-primary/90 text-white py-3 text-lg"
            disabled={files.length === 0}
          >
            <Play className="mr-2 h-5 w-5" />
            Run {utilityName}
          </Button>
        </motion.div>
      </div>
    </div>
  );
};

export default UtilityRunnerView;
