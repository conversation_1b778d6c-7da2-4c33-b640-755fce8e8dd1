import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { FileText, Image, FolderOpen, FileSpreadsheet, Video, Music, Package } from 'lucide-react';
import { motion } from 'framer-motion';
import { logger } from '../utils/logger';

const MyAppsView = () => {
  const navigate = useNavigate();
  const [installedApps, setInstalledApps] = useState([]);
  const [loading, setLoading] = useState(true);

  // Icon mapping for different app categories
  const getIconForCategory = (category) => {
    const iconMap = {
      'Image Processing': Image,
      'File Conversion': FileSpreadsheet,
      'Document': FileText,
      'Media': Video,
      'Audio': Music,
      'Utility': FolderOpen,
      'default': Package
    };
    return iconMap[category] || iconMap.default;
  };

  useEffect(() => {
    const loadInstalledApps = async () => {
      try {
        if (window.electronAPI) {
          const utilities = await window.electronAPI.getInstalledUtilities();
          logger.debug('Loaded utilities:', utilities);

          setInstalledApps(utilities.map(utility => ({
            id: utility.id,
            name: utility.name,
            version: utility.version,
            installDate: utility.installDate,
            description: `Version ${utility.version} • Installed ${new Date(utility.installDate).toLocaleDateString()}`,
            category: 'Utility', // Default category for utilities
            icon: getIconForCategory('Utility')
          })));
        } else {
          logger.info('Electron API not available - running in browser mode');
          // Fallback for browser mode
          setInstalledApps([]);
        }
      } catch (error) {
        logger.error('Failed to load installed apps:', error);
        setInstalledApps([]);
      } finally {
        setLoading(false);
      }
    };

    loadInstalledApps();
  }, []);

  const handleAppClick = (appId) => {
    navigate(`/app/${appId}`);
  };

  if (loading) {
    return (
      <div className="p-8 h-full overflow-y-auto">
        <div className="max-w-6xl mx-auto">
          <h1 className="text-3xl font-bold text-fileduck-text-primary mb-8">My Apps</h1>
          <div className="flex items-center justify-center py-12">
            <div className="text-fileduck-text-secondary">Loading apps...</div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-8 h-full overflow-y-auto">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold text-fileduck-text-primary mb-8">My Apps</h1>

        {installedApps.length === 0 ? (
          <div className="text-center py-12">
            <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Package className="h-12 w-12 text-gray-400" />
            </div>
            <h3 className="text-xl font-semibold text-fileduck-text-primary mb-2">
              No Apps Installed
            </h3>
            <p className="text-fileduck-text-secondary mb-6">
              Install apps from the Tool Store to get started.
            </p>
            <button
              onClick={() => navigate('/store')}
              className="bg-fileduck-primary hover:bg-fileduck-primary/90 text-white px-6 py-3 rounded-xl font-medium transition-colors"
            >
              Browse Tool Store
            </button>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {installedApps.map((app, index) => {
              const IconComponent = app.icon;
              return (
                <motion.div
                  key={app.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <Card
                    className="cursor-pointer hover:shadow-lg transition-all duration-200 hover:scale-105 bg-white"
                    onClick={() => handleAppClick(app.id)}
                  >
                    <CardHeader className="pb-3">
                      <div className="flex items-center space-x-3">
                        <div className="w-12 h-12 bg-fileduck-primary/10 rounded-lg flex items-center justify-center">
                          <IconComponent className="h-6 w-6 text-fileduck-primary" />
                        </div>
                        <CardTitle className="text-lg text-fileduck-text-primary">
                          {app.name}
                        </CardTitle>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-fileduck-text-secondary">
                        {app.description}
                      </p>
                    </CardContent>
                  </Card>
                </motion.div>
              );
            })}
          </div>
        )}
      </div>
    </div>
  );
};

export default MyAppsView;
