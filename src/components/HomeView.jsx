import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  Image,
  FileText,
  File,
  Music,
  Video,
  Settings,
  Info,
  Trash2
} from 'lucide-react';
import { PageWrapper } from './ui/page-wrapper';
import { MacOSButton } from './ui/macos-button';
import { Typography, listVariants, itemVariants } from '../utils/typography';
import { logger } from '../utils/logger';

// AppCard component for installed apps - Apple App Store style
const AppCard = ({ app, showDivider = true, onOpen, onUninstall, onDetails }) => {
  const IconComponent = app.icon;

  return (
    <div className="relative">
      <div className="flex items-center py-3 px-0">
        {/* App Icon */}
        <div className="w-16 h-16 bg-system-blue/10 rounded-xl flex items-center justify-center mr-4 flex-shrink-0">
          <IconComponent className="h-8 w-8 text-system-blue" />
        </div>

        {/* App Content */}
        <div className="flex-1 min-w-0 mr-3">
          <h3 className={`${Typography.body} font-medium mb-1 truncate`}>
            {app.name}
          </h3>
          <p className={`${Typography.caption} mb-2 truncate`}>
            {app.description}
          </p>
          <div className="flex items-center space-x-3 text-xs">
            <motion.button
                onClick={() => onDetails(app.id)}
                className="bg-macos-elevated hover:bg-macos-border text-macos-text-secondary rounded-full p-1.5 focus:outline-none transition-colors"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
            >
              <Info className="h-4 w-4" />
            </motion.button>
            <motion.button
                onClick={() => onUninstall(app.id)}
                className="bg-system-red/10 hover:bg-system-red/20 text-system-red rounded-full p-1.5 focus:outline-none transition-colors"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
            >
              <Trash2 className="h-4 w-4" />
            </motion.button>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex-shrink-0 flex items-center space-x-2">
          <MacOSButton
            onClick={() => onOpen(app.id)}
            variant="secondary"
            size="sm"
            className="rounded-full px-5"
          >
            Open
          </MacOSButton>
        </div>
      </div>

      {/* Divider Line */}
      {showDivider && (
        <div className="absolute bottom-0 left-20 right-0 h-px bg-macos-border"></div>
      )}
    </div>
  );
};

const HomeView = () => {
  const navigate = useNavigate();
  const [currentTime, setCurrentTime] = useState(new Date());
  const [installedApps, setInstalledApps] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000); // Update every minute

    return () => clearInterval(timer);
  }, []);

  const getGreeting = () => {
    const hour = currentTime.getHours();
    if (hour >= 5 && hour < 12) {
      return { text: 'Good morning', emoji: '🌅' };
    } else if (hour >= 12 && hour < 17) {
      return { text: 'Good afternoon', emoji: '☀️' };
    } else if (hour >= 17 && hour < 21) {
      return { text: 'Good evening', emoji: '🌆' };
    } else {
      return { text: 'Good night', emoji: '🌙' };
    }
  };

  const greeting = getGreeting();

  // Icon mapping for different app categories
  const getIconForCategory = (category) => {
    const iconMap = {
      'Image Processing': Image,
      'File Conversion': FileText,
      'Document': File,
      'Media': Video,
      'Audio': Music,
      'Utility': Settings,
      'default': Settings
    };
    return iconMap[category] || iconMap.default;
  };

  // Load installed apps from database
  const loadInstalledApps = async () => {
    try {
      if (window.electronAPI) {
        const utilities = await window.electronAPI.getInstalledUtilities();
        setInstalledApps(utilities.map((utility) => ({
          id: utility.id,
          name: utility.name,
          version: utility.version,
          installDate: utility.installDate,
          description: utility.description,
          category: utility.category || 'Utility', // Default category for utilities
          icon: getIconForCategory(utility.category || 'Utility')
        })));
      }
    } catch (error) {
      logger.error('Failed to load installed apps:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadInstalledApps();
  }, []);

  const handleAppOpen = (appId) => {
    // Remember that we came from Home tab
    sessionStorage.setItem('sourceTab', '/');
    navigate(`/app/${appId}`);
  };

  const handleAppUninstall = async (appId) => {
    try {
      if (window.electronAPI && window.electronAPI.uninstallUtility) {
        const result = await window.electronAPI.uninstallUtility(appId);
        if (result.success) {
          // Refresh the installed apps list
          await loadInstalledApps();
          logger.success(`App ${appId} uninstalled successfully`);
        } else {
          logger.error('Failed to uninstall app:', result.error);
        }
      } else {
        logger.info('Uninstall functionality only available in desktop app');
      }
    } catch (error) {
      logger.error('Error uninstalling app:', error);
    }
  };

  const handleAppDetails = (appId) => {
    // Navigate to app details page in store
    navigate(`/store/app/${appId}`);
  };

  return (
    <PageWrapper className="px-8 pt-16 pb-8 h-full overflow-y-auto">
      <div className="max-w-7xl mx-auto">
        {/* Greeting */}
        <motion.div
          className="mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <h1 className={`${Typography.title1} mb-2`}>
            Hello Amin, {greeting.text}! {greeting.emoji}
          </h1>
          <p className={`${Typography.bodySecondary} text-lg`}>
            Welcome back to FileDuck. Here are your installed apps.
          </p>
        </motion.div>

        {/* My apps Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
        >
          <div className="flex items-center justify-between mb-8">
            <h2 className={Typography.title2}>My Apps</h2>
            <MacOSButton
              onClick={() => navigate('/store')}
              variant="primary"
              className="rounded-xl"
            >
              Browse Tool Store
            </MacOSButton>
          </div>

          {installedApps.length === 0 ? (
            // Empty State
            <motion.div
              className="flex flex-col items-center justify-center py-16 text-center"
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              <div className="mb-8">
                <div className="w-24 h-24 bg-macos-surface rounded-3xl flex items-center justify-center mb-6 mx-auto shadow-lg border border-macos-border">
                  <img
                    src="/duck-icon.png"
                    alt="FileDuck Logo"
                    className="w-16 h-16"
                  />
                </div>
                <h3 className={`${Typography.title2} mb-3`}>
                  No Apps Installed
                </h3>
                <p className={`${Typography.bodySecondary} text-lg mb-8 max-w-md`}>
                  Get started by installing your first tool from the Tool Store.
                  Discover powerful utilities to enhance your workflow.
                </p>
                <MacOSButton
                  onClick={() => navigate('/store')}
                  variant="primary"
                  size="lg"
                  className="shadow-lg hover:shadow-xl"
                >
                  Install Your First App
                </MacOSButton>
              </div>
            </motion.div>
          ) : (
            // apps List - Apple App Store Style (2-column grid)
            <motion.div
              className="grid grid-cols-2 gap-x-8"
              variants={listVariants}
              initial="initial"
              animate="animate"
            >
              <div className="space-y-0">
                {installedApps.filter((_, index) => index % 2 === 0).map((app, index, filteredApps) => (
                  <motion.div key={app.id} variants={itemVariants}>
                    <AppCard
                      app={app}
                      showDivider={index < filteredApps.length - 1}
                      onOpen={handleAppOpen}
                      onUninstall={handleAppUninstall}
                      onDetails={handleAppDetails}
                    />
                  </motion.div>
                ))}
              </div>
              <div className="space-y-0">
                {installedApps.filter((_, index) => index % 2 === 1).map((app, index, filteredApps) => (
                  <motion.div key={app.id} variants={itemVariants}>
                    <AppCard
                      app={app}
                      showDivider={index < filteredApps.length - 1}
                      onOpen={handleAppOpen}
                      onUninstall={handleAppUninstall}
                      onDetails={handleAppDetails}
                    />
                  </motion.div>
                ))}
              </div>
            </motion.div>
          )}
        </motion.div>
      </div>
    </PageWrapper>
  );
};

export default HomeView;
