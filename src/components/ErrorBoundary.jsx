import React from 'react';
import { logger } from '../utils/logger';

class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { 
      hasError: false, 
      error: null, 
      errorInfo: null 
    };
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    // Log the error details
    logger.error('ErrorBoundary caught an error:', error, errorInfo);

    this.setState({
      error: error,
      errorInfo: errorInfo
    });

    // You can also log the error to an error reporting service here
    // logErrorToService(error, errorInfo);
  }

  handleCloseError = () => {
    this.setState({ 
      hasError: false, 
      error: null, 
      errorInfo: null 
    });
  };

  render() {
    if (this.state.hasError) {
      // Create a detailed error object for the dialog
      const detailedError = {
        message: this.state.error?.message || 'Component Error',
        stack: this.state.error?.stack || 'No stack trace available',
        componentStack: this.state.errorInfo?.componentStack || 'No component stack available',
        toString: () => `${this.state.error?.message || 'Unknown error'}\n\nComponent Stack:\n${this.state.errorInfo?.componentStack || 'No component stack'}\n\nError Stack:\n${this.state.error?.stack || 'No stack trace'}`
      };

      return (
        <>
          {/* Fallback UI */}
          <div className="min-h-screen bg-fileduck-bg-main flex items-center justify-center p-8">
            <div className="text-center max-w-md">
              <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              <h1 className="text-2xl font-bold text-fileduck-text-primary mb-2">
                Something went wrong
              </h1>
              <p className="text-fileduck-text-secondary mb-6">
                The application encountered an unexpected error. Please try refreshing the page.
              </p>
              <div className="space-y-3">
                <button
                  onClick={() => window.location.reload()}
                  className="w-full bg-fileduck-primary hover:bg-fileduck-primary/90 text-white px-6 py-3 rounded-lg font-medium transition-colors"
                >
                  Reload Application
                </button>
                <button
                  onClick={this.handleCloseError}
                  className="w-full border border-gray-300 hover:bg-gray-50 text-fileduck-text-primary px-6 py-3 rounded-lg font-medium transition-colors"
                >
                  Try Again
                </button>
              </div>
            </div>
          </div>

          {/* Error details in console */}
          {logger.error('Error details:', detailedError)}
        </>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
