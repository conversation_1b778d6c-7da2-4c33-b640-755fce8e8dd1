import { clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';
import _ from 'lodash';

// Tailwind CSS class utility
export function cn(...inputs) {
  return twMerge(clsx(inputs));
}

// Utility functions collection
export const utils = {
  // Lodash re-exports for convenience
  debounce: _.debounce,
  throttle: _.throttle,
  isEmpty: _.isEmpty,
  isEqual: _.isEqual,
  cloneDeep: _.cloneDeep,
  merge: _.merge,
  pick: _.pick,
  omit: _.omit,
  get: _.get,
  set: _.set,
  uniq: _.uniq,
  uniqBy: _.uniqBy,
  sortBy: _.sortBy,
  groupBy: _.groupBy,
  keyBy: _.keyBy,
  mapValues: _.mapValues,
  mapKeys: _.mapKeys,
  filter: _.filter,
  find: _.find,
  findIndex: _.findIndex,
  map: _.map,
  reduce: _.reduce,
  forEach: _.forEach,
  some: _.some,
  every: _.every,
  includes: _.includes,
  startsWith: _.startsWith,
  endsWith: _.endsWith,
  trim: _.trim,
  padStart: _.padStart,
  padEnd: _.padEnd,
  camelCase: _.camelCase,
  kebabCase: _.kebabCase,
  snakeCase: _.snakeCase,
  upperFirst: _.upperFirst,
  lowerFirst: _.lowerFirst,
  range: _.range,
  random: _.random,
  shuffle: _.shuffle,
  sample: _.sample,
  sampleSize: _.sampleSize,
  chunk: _.chunk,
  flatten: _.flatten,
  compact: _.compact,

  // Type guards
  isString: (value) => typeof value === 'string',
  isNumber: (value) => typeof value === 'number' && !isNaN(value),
  isBoolean: (value) => typeof value === 'boolean',
  isArray: Array.isArray,
  isObject: (value) =>
    value !== null && typeof value === 'object' && !Array.isArray(value),
  isFunction: (value) => typeof value === 'function',

  // String utilities
  capitalize: (str) => str.charAt(0).toUpperCase() + str.slice(1),
  truncate: (str, length) =>
    str.length > length ? str.slice(0, length) + '...' : str,
  slugify: (str) =>
    str.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, ''),

  // Number utilities
  formatBytes: (bytes, decimals = 2) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
  },

  formatNumber: (num) => new Intl.NumberFormat().format(num),

  // Date utilities
  formatDate: (date) => {
    const d = new Date(date);
    return d.toLocaleDateString();
  },

  formatDateTime: (date) => {
    const d = new Date(date);
    return d.toLocaleString();
  },

  // Validation utilities
  isValidEmail: (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  },

  isValidUrl: (url) => {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  },

  // File utilities
  getFileExtension: (filename) => {
    return filename.split('.').pop()?.toLowerCase() || '';
  },

  getMimeType: (filename) => {
    const ext = utils.getFileExtension(filename);
    const mimeTypes = {
      'jpg': 'image/jpeg',
      'jpeg': 'image/jpeg',
      'png': 'image/png',
      'gif': 'image/gif',
      'pdf': 'application/pdf',
      'txt': 'text/plain',
      'csv': 'text/csv',
      'json': 'application/json',
      'xml': 'application/xml',
      'zip': 'application/zip',
    };
    return mimeTypes[ext] || 'application/octet-stream';
  },

  // Error handling utilities
  safeJsonParse: (json, fallback) => {
    try {
      return JSON.parse(json);
    } catch {
      return fallback;
    }
  },

  // Promise utilities
  sleep: (ms) => new Promise(resolve => setTimeout(resolve, ms)),

  timeout: (promise, ms) => {
    return Promise.race([
      promise,
      new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Operation timed out')), ms)
      )
    ]);
  },
};
