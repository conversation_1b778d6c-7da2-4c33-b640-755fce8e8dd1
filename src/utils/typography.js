// macOS-inspired Typography System
// Maintains Rubik font preference while adding system font fallbacks

export const Typography = {
  // Large titles (24px)
  title1: 'text-2xl font-bold tracking-tight text-macos-text-primary font-rubik',

  // Section headers (20px)
  title2: 'text-xl font-semibold tracking-tight text-macos-text-primary font-rubik',

  // Subsection headers (16px)
  title3: 'text-lg font-semibold text-macos-text-primary font-rubik',

  // Body text (13px)
  body: 'text-sm text-macos-text-primary font-rubik',

  // Secondary text (13px)
  bodySecondary: 'text-sm text-macos-text-secondary font-rubik',

  // Small text (12px)
  caption: 'text-xs text-macos-text-secondary font-rubik',

  // Tiny text (11px)
  footnote: 'text-xs text-macos-text-tertiary font-rubik',

  // Button text
  button: 'text-sm font-medium text-macos-text-primary font-rubik',

  // Label text
  label: 'text-sm font-medium text-macos-text-primary font-rubik',
};

// Animation easing curves
export const easings = {
  smooth: [0.4, 0, 0.1, 0.25, 1],
  snappy: [0.4, 0, 0.2, 1],
  gentle: [0.25, 1, 0.3, 1],
};

// Common animation variants for Framer Motion
export const fadeIn = {
  initial: { opacity: 0, y: 10 },
  animate: { opacity: 1, y: 0 },
  exit: { opacity: 0, y: -10 },
  transition: { duration: 0.2, ease: easings.smooth }
};

export const scaleIn = {
  initial: { opacity: 0, scale: 0.95 },
  animate: { opacity: 1, scale: 1 },
  exit: { opacity: 0, scale: 0.95 },
  transition: { duration: 0.15, ease: easings.snappy }
};

export const slideIn = {
  initial: { opacity: 0, x: -20 },
  animate: { opacity: 1, x: 0 },
  exit: { opacity: 0, x: 20 },
  transition: { duration: 0.2, ease: easings.smooth }
};

export const slideInFromRight = {
  initial: { opacity: 0, x: 20 },
  animate: { opacity: 1, x: 0 },
  exit: { opacity: 0, x: -20 },
  transition: { duration: 0.2, ease: easings.smooth }
};

// Staggered list animations
export const listVariants = {
  animate: {
    transition: {
      staggerChildren: 0.05
    }
  }
};

export const itemVariants = {
  initial: { opacity: 0, y: 10 },
  animate: { opacity: 1, y: 0 }
};

// Hover and interactive states
export const interactiveStates = {
  base: 'transition-all duration-150 ease-out',
  hover: 'hover:scale-105 hover:shadow-lg',
  active: 'active:scale-95',
  focus: 'focus:outline-none focus:ring-2 focus:ring-macos-accent focus:ring-opacity-50',
};
