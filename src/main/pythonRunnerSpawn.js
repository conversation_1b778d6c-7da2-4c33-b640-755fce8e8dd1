const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');
const { app } = require('electron');

/**
 * <PERSON>Runner - Handles Python utility execution using child_process.spawn()
 * Provides secure execution of Python utilities with dependency management
 */
class PythonRunner {
  constructor() {
    this.installedPackages = new Set();
    this.logDir = path.join(app.getPath('userData'), 'logs');
    this.ensureLogDirectory();
    console.log('PythonRunner initialized (spawn-based)');
  }

  /**
   * Ensure log directory exists
   */
  ensureLogDirectory() {
    if (!fs.existsSync(this.logDir)) {
      fs.mkdirSync(this.logDir, { recursive: true });
    }
  }

  /**
   * Log to both console and file
   */
  log(message, level = 'info') {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] [${level.toUpperCase()}] ${message}`;
    
    // Console logging
    console.log(`[PythonRunner] ${logMessage}`);
    
    // File logging
    const logFile = path.join(this.logDir, 'python-runner.log');
    fs.appendFileSync(logFile, logMessage + '\n');
  }

  /**
   * Install Python package using pip
   */
  async installPackage(packageName) {
    if (this.installedPackages.has(packageName)) {
      this.log(`Package ${packageName} already installed, skipping`);
      return true;
    }

    return new Promise((resolve, reject) => {
      this.log(`Installing Python package: ${packageName}`);
      
      const pip = spawn('pip3', ['install', packageName]);
      let output = '';
      let error = '';

      pip.stdout.on('data', (data) => {
        const text = data.toString();
        output += text;
        this.log(`pip stdout: ${text.trim()}`);
      });

      pip.stderr.on('data', (data) => {
        const text = data.toString();
        error += text;
        this.log(`pip stderr: ${text.trim()}`, 'warn');
      });

      pip.on('close', (code) => {
        if (code === 0) {
          this.installedPackages.add(packageName);
          this.log(`Successfully installed package: ${packageName}`);
          resolve(true);
        } else {
          this.log(`Failed to install package ${packageName}: ${error}`, 'error');
          reject(new Error(`pip install failed: ${error}`));
        }
      });
    });
  }

  /**
   * Install dependencies from config.json requirements
   */
  async installDependencies(requirements) {
    if (!requirements || !Array.isArray(requirements)) {
      this.log('No requirements to install');
      return;
    }

    this.log(`Installing ${requirements.length} dependencies: ${requirements.join(', ')}`);
    
    for (const packageName of requirements) {
      try {
        await this.installPackage(packageName);
      } catch (error) {
        this.log(`Failed to install ${packageName}: ${error.message}`, 'error');
        // Continue with other packages even if one fails
      }
    }
  }

  /**
   * Run Python utility using spawn
   */
  async runPythonUtility(scriptPath, inputData) {
    return new Promise((resolve, reject) => {
      this.log(`Executing Python script: ${scriptPath}`);
      this.log(`Input data: ${JSON.stringify(inputData).substring(0, 200)}...`);

      const python = spawn('python3', [scriptPath]);
      let output = '';
      let error = '';

      // Send input data to Python script via stdin
      python.stdin.write(JSON.stringify(inputData));
      python.stdin.end();

      python.stdout.on('data', (data) => {
        const text = data.toString();
        output += text;
        this.log(`Python stdout: ${text.trim()}`);
      });

      python.stderr.on('data', (data) => {
        const text = data.toString();
        error += text;
        this.log(`Python stderr: ${text.trim()}`, 'warn');
      });

      python.on('close', (code) => {
        this.log(`Python process exited with code: ${code}`);
        
        if (code === 0) {
          try {
            // Try to parse output as JSON
            const result = JSON.parse(output.trim());
            this.log('Python execution successful');
            resolve(result);
          } catch (parseError) {
            // If not JSON, return raw output
            this.log('Python output is not JSON, returning raw output');
            resolve({ status: 'success', output: output.trim() });
          }
        } else {
          this.log(`Python execution failed: ${error}`, 'error');
          reject(new Error(`Python execution failed: ${error}`));
        }
      });

      python.on('error', (err) => {
        this.log(`Python process error: ${err.message}`, 'error');
        reject(new Error(`Failed to start Python process: ${err.message}`));
      });
    });
  }

  /**
   * Process file data with Python utility
   */
  async processFileData(scriptPath, fileData, config) {
    try {
      this.log(`Processing ${fileData.length} files with Python utility`);
      
      const results = [];
      
      for (const file of fileData) {
        this.log(`Processing file: ${file.filename}`);
        
        const inputData = {
          filename: file.filename,
          content: file.content || file.data,
          config: config || {}
        };

        try {
          const result = await this.runPythonUtility(scriptPath, inputData);
          results.push({
            filename: file.filename,
            success: true,
            result: result
          });
        } catch (error) {
          this.log(`Failed to process ${file.filename}: ${error.message}`, 'error');
          results.push({
            filename: file.filename,
            success: false,
            error: error.message
          });
        }
      }

      this.log(`Processed ${results.length} files`);
      return { status: 'success', results: results };
      
    } catch (error) {
      this.log(`Process file data error: ${error.message}`, 'error');
      throw error;
    }
  }

  /**
   * Save output to converted folder
   */
  async saveToConverted(content, filename) {
    try {
      const convertedDir = path.join(process.cwd(), 'converted');
      if (!fs.existsSync(convertedDir)) {
        fs.mkdirSync(convertedDir, { recursive: true });
      }

      const filePath = path.join(convertedDir, filename);
      fs.writeFileSync(filePath, content, 'utf8');
      this.log(`Saved output to: ${filePath}`);
      return filePath;
    } catch (error) {
      this.log(`Failed to save file: ${error.message}`, 'error');
      throw error;
    }
  }
}

module.exports = PythonRunner;
