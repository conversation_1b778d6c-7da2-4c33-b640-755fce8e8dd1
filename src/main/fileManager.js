const fs = require('fs');
const path = require('path');
const { app, dialog } = require('electron');

class FileManager {
  constructor() {
    this.userDataPath = app.getPath('userData');
    this.allowedPaths = [
      app.getPath('documents'),
      app.getPath('downloads'),
      app.getPath('desktop'),
      app.getPath('pictures'),
      this.userDataPath
    ];
  }

  /**
   * Validate if a path is within allowed directories
   */
  isPathAllowed(filePath) {
    const resolvedPath = path.resolve(filePath);
    return this.allowedPaths.some(allowedPath => {
      const resolvedAllowedPath = path.resolve(allowedPath);
      return resolvedPath.startsWith(resolvedAllowedPath);
    });
  }

  /**
   * Show file picker dialog
   */
  async showOpenDialog(options = {}) {
    try {
      const defaultOptions = {
        properties: ['openFile'],
        filters: [
          { name: 'All Files', extensions: ['*'] }
        ]
      };

      const dialogOptions = { ...defaultOptions, ...options };
      const result = await dialog.showOpenDialog(dialogOptions);

      if (result.canceled) {
        return { canceled: true };
      }

      return {
        canceled: false,
        filePaths: result.filePaths
      };
    } catch (error) {
      console.error('File dialog error:', error);
      throw error;
    }
  }

  /**
   * Show save dialog
   */
  async showSaveDialog(options = {}) {
    try {
      const defaultOptions = {
        filters: [
          { name: 'All Files', extensions: ['*'] }
        ]
      };

      const dialogOptions = { ...defaultOptions, ...options };
      const result = await dialog.showSaveDialog(dialogOptions);

      if (result.canceled) {
        return { canceled: true };
      }

      return {
        canceled: false,
        filePath: result.filePath
      };
    } catch (error) {
      console.error('Save dialog error:', error);
      throw error;
    }
  }

  /**
   * Read file with security checks
   */
  async readFile(filePath) {
    try {
      if (!this.isPathAllowed(filePath)) {
        throw new Error('Access denied: Path not allowed');
      }

      if (!fs.existsSync(filePath)) {
        throw new Error('File not found');
      }

      const stats = fs.statSync(filePath);
      if (!stats.isFile()) {
        throw new Error('Path is not a file');
      }

      // Check file size (limit to 100MB)
      const maxSize = 100 * 1024 * 1024;
      if (stats.size > maxSize) {
        throw new Error('File too large (max 100MB)');
      }

      const data = fs.readFileSync(filePath);
      
      return {
        data,
        size: stats.size,
        name: path.basename(filePath),
        extension: path.extname(filePath),
        mimeType: this.getMimeType(filePath)
      };
    } catch (error) {
      console.error('Read file error:', error);
      throw error;
    }
  }

  /**
   * Write file with security checks
   */
  async writeFile(filePath, data, options = {}) {
    try {
      if (!this.isPathAllowed(filePath)) {
        throw new Error('Access denied: Path not allowed');
      }

      // Ensure directory exists
      const directory = path.dirname(filePath);
      if (!fs.existsSync(directory)) {
        fs.mkdirSync(directory, { recursive: true });
      }

      // Check if file already exists and handle overwrite
      if (fs.existsSync(filePath) && !options.overwrite) {
        throw new Error('File already exists');
      }

      fs.writeFileSync(filePath, data);

      return {
        success: true,
        filePath,
        size: data.length
      };
    } catch (error) {
      console.error('Write file error:', error);
      throw error;
    }
  }

  /**
   * Get file information
   */
  async getFileInfo(filePath) {
    try {
      if (!this.isPathAllowed(filePath)) {
        throw new Error('Access denied: Path not allowed');
      }

      if (!fs.existsSync(filePath)) {
        throw new Error('File not found');
      }

      const stats = fs.statSync(filePath);
      
      return {
        name: path.basename(filePath),
        path: filePath,
        size: stats.size,
        extension: path.extname(filePath),
        mimeType: this.getMimeType(filePath),
        isFile: stats.isFile(),
        isDirectory: stats.isDirectory(),
        created: stats.birthtime,
        modified: stats.mtime,
        accessed: stats.atime
      };
    } catch (error) {
      console.error('Get file info error:', error);
      throw error;
    }
  }

  /**
   * List directory contents
   */
  async listDirectory(dirPath) {
    try {
      if (!this.isPathAllowed(dirPath)) {
        throw new Error('Access denied: Path not allowed');
      }

      if (!fs.existsSync(dirPath)) {
        throw new Error('Directory not found');
      }

      const stats = fs.statSync(dirPath);
      if (!stats.isDirectory()) {
        throw new Error('Path is not a directory');
      }

      const items = fs.readdirSync(dirPath);
      const fileList = [];

      for (const item of items) {
        const itemPath = path.join(dirPath, item);
        const itemStats = fs.statSync(itemPath);
        
        fileList.push({
          name: item,
          path: itemPath,
          size: itemStats.size,
          isFile: itemStats.isFile(),
          isDirectory: itemStats.isDirectory(),
          extension: path.extname(item),
          modified: itemStats.mtime
        });
      }

      return fileList;
    } catch (error) {
      console.error('List directory error:', error);
      throw error;
    }
  }

  /**
   * Create temporary file
   */
  async createTempFile(data, extension = '.tmp') {
    try {
      const tempDir = path.join(this.userDataPath, 'temp');
      if (!fs.existsSync(tempDir)) {
        fs.mkdirSync(tempDir, { recursive: true });
      }

      const tempFileName = `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}${extension}`;
      const tempFilePath = path.join(tempDir, tempFileName);

      fs.writeFileSync(tempFilePath, data);

      return {
        path: tempFilePath,
        name: tempFileName,
        size: data.length
      };
    } catch (error) {
      console.error('Create temp file error:', error);
      throw error;
    }
  }

  /**
   * Clean up temporary files
   */
  async cleanupTempFiles() {
    try {
      const tempDir = path.join(this.userDataPath, 'temp');
      if (!fs.existsSync(tempDir)) {
        return;
      }

      const files = fs.readdirSync(tempDir);
      let cleanedCount = 0;

      for (const file of files) {
        const filePath = path.join(tempDir, file);
        const stats = fs.statSync(filePath);
        
        // Remove files older than 1 hour
        const oneHourAgo = Date.now() - (60 * 60 * 1000);
        if (stats.mtime.getTime() < oneHourAgo) {
          fs.unlinkSync(filePath);
          cleanedCount++;
        }
      }

      console.log(`Cleaned up ${cleanedCount} temporary files`);
      return cleanedCount;
    } catch (error) {
      console.error('Cleanup temp files error:', error);
      throw error;
    }
  }

  /**
   * Get MIME type based on file extension
   */
  getMimeType(filePath) {
    const ext = path.extname(filePath).toLowerCase();
    const mimeTypes = {
      '.txt': 'text/plain',
      '.html': 'text/html',
      '.css': 'text/css',
      '.js': 'application/javascript',
      '.json': 'application/json',
      '.png': 'image/png',
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.gif': 'image/gif',
      '.webp': 'image/webp',
      '.svg': 'image/svg+xml',
      '.pdf': 'application/pdf',
      '.zip': 'application/zip',
      '.mp3': 'audio/mpeg',
      '.mp4': 'video/mp4',
      '.wav': 'audio/wav'
    };

    return mimeTypes[ext] || 'application/octet-stream';
  }

  /**
   * Get allowed paths for security
   */
  getAllowedPaths() {
    return [...this.allowedPaths];
  }
}

module.exports = FileManager;
