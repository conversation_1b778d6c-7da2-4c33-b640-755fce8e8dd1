const { contextBridge, ipc<PERSON>ender<PERSON> } = require('electron');

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // System methods
  platform: process.platform,
  toggleDevTools: () => ipcRenderer.invoke('toggle-devtools'),

  // Utility management methods (as per core-tech.md spec)
  getInstalledUtilities: () => ipcRenderer.invoke('get-installed-utilities'),
  installUtility: (downloadUrl) => ipcRenderer.invoke('install-utility', downloadUrl),
  installUtilityFromLocalPath: (localZipPath) => ipcRenderer.invoke('install-utility-from-local', localZipPath),
  uninstallUtility: (utilityId) => ipcRenderer.invoke('uninstall-utility', utilityId),
  runUtility: (utilityId, fileData, config) => ipcRenderer.invoke('run-utility', utilityId, fileData, config),

  // File management methods
  showOpenDialog: (options) => ipcRenderer.invoke('show-open-dialog', options),
  showSaveDialog: (options) => ipcRenderer.invoke('show-save-dialog', options),
  readFile: (filePath) => ipcRenderer.invoke('read-file', filePath),
  writeFile: (filePath, data, options) => ipcRenderer.invoke('write-file', filePath, data, options),
  openFolder: (folderPath) => ipcRenderer.invoke('open-folder', folderPath),

  // WASM execution methods
  executeWasm: (wasmPath, functionName, ...args) => ipcRenderer.invoke('execute-wasm', wasmPath, functionName, ...args),
  processFileWasm: (wasmPath, fileData, config) => ipcRenderer.invoke('process-file-wasm', wasmPath, fileData, config),

  // Python execution methods
  executePython: (pythonPath, functionName, ...args) => ipcRenderer.invoke('execute-python', pythonPath, functionName, ...args),
  processFilePython: (pythonPath, fileData, config) => ipcRenderer.invoke('process-file-python', pythonPath, fileData, config),
  processFilesPython: (pythonPath, filesData, config) => ipcRenderer.invoke('process-files-python', pythonPath, filesData, config),

  // Database management (for debug dashboard)
  getUtilityById: (id) => ipcRenderer.invoke('get-utility-by-id', id),
  deleteUtility: (id) => ipcRenderer.invoke('delete-utility', id),
  clearDatabase: () => ipcRenderer.invoke('clear-database'),
  addUtility: (data) => ipcRenderer.invoke('add-utility', data),
  updateUtility: (id, data) => ipcRenderer.invoke('update-utility', id, data),
  getUserDataPath: () => ipcRenderer.invoke('get-user-data-path')
});
