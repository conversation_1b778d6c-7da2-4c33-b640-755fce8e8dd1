const { app } = require('electron');
const path = require('path');
const fs = require('fs');
const https = require('https');
const http = require('http');
const AdmZip = require('adm-zip');

class ContextBridge {
  constructor() {
    this.userDataPath = app.getPath('userData');
    this.utilitiesPath = path.join(this.userDataPath, 'utilities');
    this.installedJsonPath = path.join(this.userDataPath, 'installed.json');
    this.ensureDirectories();
  }

  ensureDirectories() {
    // Ensure userData directory exists
    if (!fs.existsSync(this.userDataPath)) {
      fs.mkdirSync(this.userDataPath, { recursive: true });
    }

    // Ensure utilities directory exists
    if (!fs.existsSync(this.utilitiesPath)) {
      fs.mkdirSync(this.utilitiesPath, { recursive: true });
    }

    // Ensure installed.json exists
    if (!fs.existsSync(this.installedJsonPath)) {
      const initialData = { installed: [] };
      fs.writeFileSync(this.installedJsonPath, JSON.stringify(initialData, null, 2));
    }
  }

  /**
   * Reads /userData/installed.json and returns the installed array
   * Also cleans up orphaned entries
   */
  getInstalledUtilities() {
    try {
      const data = fs.readFileSync(this.installedJsonPath, 'utf8');
      const parsed = JSON.parse(data);
      const utilities = parsed.installed || [];

      // Clean up orphaned entries (database entries without corresponding directories)
      const validUtilities = utilities.filter(utility => {
        const utilityDir = path.dirname(utility.localPath);
        const exists = fs.existsSync(utilityDir);
        if (!exists) {
          console.log(`[ContextBridge] Removing orphaned database entry for ${utility.id}`);
        }
        return exists;
      });

      // Update the database if we removed any orphaned entries
      if (validUtilities.length !== utilities.length) {
        const updatedData = { installed: validUtilities };
        fs.writeFileSync(this.installedJsonPath, JSON.stringify(updatedData, null, 2));
        console.log(`[ContextBridge] Cleaned up ${utilities.length - validUtilities.length} orphaned database entries`);
      }

      return validUtilities;
    } catch (error) {
      console.error('Error reading installed.json:', error);
      return [];
    }
  }

  /**
   * Downloads the .zip package from downloadUrl
   * Unzips the package into /userData/utilities/{utility-name-v{version}}
   * Adds a new entry to the /userData/installed.json database
   */
  async installUtility(downloadUrl) {
    try {
      // Download the zip file to temp location
      const tempZipPath = path.join(this.userDataPath, 'temp_download.zip');
      await this.downloadFile(downloadUrl, tempZipPath);

      // Extract to get config.json to read metadata
      const tempExtractPath = path.join(this.userDataPath, 'temp_extract');
      if (fs.existsSync(tempExtractPath)) {
        fs.rmSync(tempExtractPath, { recursive: true, force: true });
      }
      fs.mkdirSync(tempExtractPath, { recursive: true });

      const zip = new AdmZip(tempZipPath);
      zip.extractAllTo(tempExtractPath, true);

      // Read config.json to get metadata
      // Try multiple locations for config.json
      let configPath = null;

      const possibleConfigPaths = [
        path.join(tempExtractPath, 'dist', 'config.json'),  // New format in dist/
        path.join(tempExtractPath, 'config.json')           // New format in root
      ];

      for (const possiblePath of possibleConfigPaths) {
        if (fs.existsSync(possiblePath)) {
          configPath = possiblePath;
          break;
        }
      }

      if (!configPath) {
        throw new Error('config.json not found in package. Please check ' + tempExtractPath);
      }

      const utilityConfig = JSON.parse(fs.readFileSync(configPath, 'utf8'));
      const { name, version } = utilityConfig;

      if (!name || !version) {
        throw new Error('config.json must contain name and version');
      }

      // Create final utility directory
      const utilityId = `${name.toLowerCase().replace(/\s+/g, '-')}-v${version}`;

      // Log format type after utilityId is defined
      console.log(`[ContextBridge] Using config.json format for ${utilityId}`);
      const finalUtilityPath = path.join(this.utilitiesPath, utilityId);

      if (fs.existsSync(finalUtilityPath)) {
        throw new Error(`Utility ${utilityId} is already installed`);
      }

      // Move extracted files to final location
      fs.renameSync(tempExtractPath, finalUtilityPath);

      // Verify required files exist based on language
      let mainExecutableFound = false;
      let actualMainPath = null;

      if (utilityConfig.language === 'python') {
        // Use the 'main' field, otherwise default to 'main.py'
        const mainFileName = utilityConfig.main || 'main.py';

        // Try multiple possible locations for the main file
        const possiblePaths = [
          path.join(finalUtilityPath, mainFileName),           // Direct path from config
          path.join(finalUtilityPath, 'dist', mainFileName),   // In dist/ subdirectory
          path.join(finalUtilityPath, 'src', mainFileName),    // In src/ subdirectory
          path.join(finalUtilityPath, 'main.py')               // Default fallback
        ];

        for (const possiblePath of possiblePaths) {
          if (fs.existsSync(possiblePath)) {
            mainExecutableFound = true;
            actualMainPath = possiblePath;
            console.log(`[ContextBridge] Found Python main file at: ${possiblePath}`);
            break;
          }
        }
      } else if (utilityConfig.language === 'shell') {
        const possiblePaths = [
          path.join(finalUtilityPath, 'main.sh'),
          path.join(finalUtilityPath, 'dist', 'main.sh'),
          path.join(finalUtilityPath, 'src', 'main.sh')
        ];

        for (const possiblePath of possiblePaths) {
          if (fs.existsSync(possiblePath)) {
            mainExecutableFound = true;
            actualMainPath = possiblePath;
            console.log(`[ContextBridge] Found shell main file at: ${possiblePath}`);
            break;
          }
        }
      } else {
        throw new Error(`Unsupported language: ${utilityConfig.language}. Only 'python' and 'shell' are supported.`);
      }

      if (!mainExecutableFound) {
        const expectedFile = utilityConfig.language === 'python' ? (utilityConfig.main || 'main.py') : 'main.sh';
        throw new Error(`${expectedFile} not found in package for ${utilityConfig.language} utility`);
      }

      // Update installed.json
      const installedData = this.getInstalledData();
      const configFileName = path.basename(configPath);
      const configRelativePath = configPath.includes('dist/') ?
        path.join('dist', configFileName) : configFileName;

      const newEntry = {
        id: utilityId,
        name: utilityConfig.name,
        version: utilityConfig.version,
        installDate: new Date().toISOString(),
        localPath: path.join(finalUtilityPath, configRelativePath),
        language: utilityConfig.language || 'wasm',
        requirements: utilityConfig.requirements || [],
        format: 'config.json'
      };

      console.log(`[ContextBridge] Installing ${utilityId} with ${configFileName} format`);

      installedData.installed.push(newEntry);
      fs.writeFileSync(this.installedJsonPath, JSON.stringify(installedData, null, 2));

      // Cleanup temp files
      if (fs.existsSync(tempZipPath)) {
        fs.unlinkSync(tempZipPath);
      }

      console.log(`Utility ${name} v${version} installed successfully`);
      return { success: true, utilityId };

    } catch (error) {
      console.error('Install utility error:', error);
      
      // Cleanup on failure
      const tempZipPath = path.join(this.userDataPath, 'temp_download.zip');
      const tempExtractPath = path.join(this.userDataPath, 'temp_extract');
      
      if (fs.existsSync(tempZipPath)) {
        fs.unlinkSync(tempZipPath);
      }
      if (fs.existsSync(tempExtractPath)) {
        fs.rmSync(tempExtractPath, { recursive: true, force: true });
      }
      
      throw error;
    }
  }

  /**
   * Developer-only function that opens a file dialog to select a local .zip
   * and triggers the same installation and database-update logic
   */
  async installUtilityFromLocalPath(localZipPath) {
    try {
      if (!fs.existsSync(localZipPath)) {
        throw new Error('Local zip file not found');
      }

      // Handle local installation directly without HTTP conversion
      const tempZipPath = path.join(this.userDataPath, 'temp_download.zip');

      // Copy local file to temp location
      fs.copyFileSync(localZipPath, tempZipPath);

      // Extract to get utility.json to read metadata
      const tempExtractPath = path.join(this.userDataPath, 'temp_extract');
      if (fs.existsSync(tempExtractPath)) {
        fs.rmSync(tempExtractPath, { recursive: true, force: true });
      }
      fs.mkdirSync(tempExtractPath, { recursive: true });

      const zip = new AdmZip(tempZipPath);
      zip.extractAllTo(tempExtractPath, true);

      // Read config.json to get metadata
      // Try multiple locations for config.json
      let configPath = null;

      const possibleConfigPaths = [
        path.join(tempExtractPath, 'dist', 'config.json'),  // New format in dist/
        path.join(tempExtractPath, 'config.json')           // New format in root
      ];

      for (const possiblePath of possibleConfigPaths) {
        if (fs.existsSync(possiblePath)) {
          configPath = possiblePath;
          break;
        }
      }

      if (!configPath) {
        throw new Error('config.json not found in package. Please check ' + tempExtractPath);
      }

      const utilityConfig = JSON.parse(fs.readFileSync(configPath, 'utf8'));
      const { name, version } = utilityConfig;

      if (!name || !version) {
        throw new Error('config.json must contain name and version');
      }

      // Create final utility directory
      const utilityId = `${name.toLowerCase().replace(/\s+/g, '-')}-v${version}`;

      // Log format type after utilityId is defined
      console.log(`[ContextBridge] Using config.json format for ${utilityId}`);
      const finalUtilityPath = path.join(this.utilitiesPath, utilityId);

      // Check if utility is already installed (both directory and database entry)
      const installedUtilities = this.getInstalledUtilities();
      const existingUtility = installedUtilities.find(u => u.id === utilityId);

      if (fs.existsSync(finalUtilityPath) && existingUtility) {
        throw new Error(`Utility ${utilityId} is already installed`);
      }

      // Clean up orphaned entries (directory exists but no database entry, or vice versa)
      if (fs.existsSync(finalUtilityPath) && !existingUtility) {
        console.log(`[ContextBridge] Cleaning up orphaned directory for ${utilityId}`);
        fs.rmSync(finalUtilityPath, { recursive: true, force: true });
      }

      if (!fs.existsSync(finalUtilityPath) && existingUtility) {
        console.log(`[ContextBridge] Cleaning up orphaned database entry for ${utilityId}`);
        const installedData = this.getInstalledData();
        installedData.installed = installedData.installed.filter(u => u.id !== utilityId);
        fs.writeFileSync(this.installedJsonPath, JSON.stringify(installedData, null, 2));
      }

      // Move extracted files to final location
      fs.renameSync(tempExtractPath, finalUtilityPath);

      // Verify required files exist based on language
      let mainExecutableFound = false;
      let actualMainPath = null;

      if (utilityConfig.language === 'python') {
        // For new format, use the 'main' field, otherwise default to 'main.py'
        const mainFileName = utilityConfig.main || 'main.py';

        // Try multiple possible locations for the main file
        const possiblePaths = [
          path.join(finalUtilityPath, mainFileName),           // Direct path from config
          path.join(finalUtilityPath, 'dist', mainFileName),   // In dist/ subdirectory
          path.join(finalUtilityPath, 'src', mainFileName),    // In src/ subdirectory
          path.join(finalUtilityPath, 'main.py')               // Default fallback
        ];

        for (const possiblePath of possiblePaths) {
          if (fs.existsSync(possiblePath)) {
            mainExecutableFound = true;
            actualMainPath = possiblePath;
            console.log(`[ContextBridge] Found Python main file at: ${possiblePath}`);
            break;
          }
        }
      } else if (utilityConfig.language === 'shell') {
        const possiblePaths = [
          path.join(finalUtilityPath, 'main.sh'),
          path.join(finalUtilityPath, 'dist', 'main.sh'),
          path.join(finalUtilityPath, 'src', 'main.sh')
        ];

        for (const possiblePath of possiblePaths) {
          if (fs.existsSync(possiblePath)) {
            mainExecutableFound = true;
            actualMainPath = possiblePath;
            console.log(`[ContextBridge] Found shell main file at: ${possiblePath}`);
            break;
          }
        }
      } else {
        throw new Error(`Unsupported language: ${utilityConfig.language}. Only 'python' and 'shell' are supported.`);
      }

      if (!mainExecutableFound) {
        const expectedFile = utilityConfig.language === 'python' ? (utilityConfig.main || 'main.py') : 'main.sh';
        throw new Error(`${expectedFile} not found in package for ${utilityConfig.language} utility`);
      }

      // Install Python dependencies if specified
      if (utilityConfig.language === 'python' && utilityConfig.requirements) {
        console.log(`Installing Python dependencies: ${utilityConfig.requirements.join(', ')}`);
        const PythonRunner = require('./pythonRunnerSpawn');
        const pythonRunner = new PythonRunner();
        try {
          await pythonRunner.installDependencies(utilityConfig.requirements);
        } catch (error) {
          console.warn('Failed to install some Python dependencies:', error.message);
          // Continue installation even if dependencies fail
        }
      }

      // Update installed.json
      const installedData = this.getInstalledData();
      const configFileName = path.basename(configPath);
      const configRelativePath = configPath.includes('dist/') ?
        path.join('dist', configFileName) : configFileName;

      const newEntry = {
        id: utilityId,
        name: utilityConfig.name,
        version: utilityConfig.version,
        installDate: new Date().toISOString(),
        localPath: path.join(finalUtilityPath, configRelativePath),
        language: utilityConfig.language || 'wasm',
        requirements: utilityConfig.requirements || [],
        format: 'config.json'
      };

      console.log(`[ContextBridge] Installing ${utilityId} with ${configFileName} format`);

      installedData.installed.push(newEntry);
      fs.writeFileSync(this.installedJsonPath, JSON.stringify(installedData, null, 2));

      // Cleanup temp files
      if (fs.existsSync(tempZipPath)) {
        fs.unlinkSync(tempZipPath);
      }

      console.log(`Utility ${name} v${version} installed successfully from local path`);
      return { success: true, utilityId };

    } catch (error) {
      console.error('Install utility from local path error:', error);

      // Cleanup on failure
      const tempZipPath = path.join(this.userDataPath, 'temp_download.zip');
      const tempExtractPath = path.join(this.userDataPath, 'temp_extract');

      if (fs.existsSync(tempZipPath)) {
        fs.unlinkSync(tempZipPath);
      }
      if (fs.existsSync(tempExtractPath)) {
        fs.rmSync(tempExtractPath, { recursive: true, force: true });
      }

      throw error;
    }
  }

  /**
   * Finds the utility by id in the installed.json database,
   * detects runtime type (WASM/Python), and returns appropriate execution info
   */
  async runUtility(utilityId, fileData, config) {
    try {
      const installedUtilities = this.getInstalledUtilities();
      const utility = installedUtilities.find(u => u.id === utilityId);

      if (!utility) {
        throw new Error(`Utility ${utilityId} not found`);
      }

      const utilityDir = path.dirname(utility.localPath);

      // Load utility configuration
      const utilityConfig = JSON.parse(fs.readFileSync(utility.localPath, 'utf8'));
      console.log(`[ContextBridge] Loaded config from: ${utility.localPath}`);
      console.log(`[ContextBridge] Config structure:`, utilityConfig);

      // Detect runtime type based on language field
      let runtimeType = 'python'; // default to python
      let executablePath = null;

      if (utilityConfig.language === 'python') {
        // Use spawn-based execution for Python
        const pythonFile = utilityConfig.main || 'main.py';
        runtimeType = 'python-spawn';

        // Try multiple possible locations for the Python file
        const possiblePaths = [
          path.join(utilityDir, pythonFile),           // Direct path
          path.join(utilityDir, 'dist', pythonFile),   // In dist/ subdirectory
          path.join(utilityDir, 'src', pythonFile),    // In src/ subdirectory
        ];

        for (const possiblePath of possiblePaths) {
          if (fs.existsSync(possiblePath)) {
            executablePath = possiblePath;
            console.log(`[ContextBridge] Found Python executable at: ${possiblePath}`);
            break;
          }
        }
      } else if (utilityConfig.language === 'shell') {
        const possiblePaths = [
          path.join(utilityDir, 'main.sh'),
          path.join(utilityDir, 'dist', 'main.sh'),
          path.join(utilityDir, 'src', 'main.sh')
        ];

        for (const possiblePath of possiblePaths) {
          if (fs.existsSync(possiblePath)) {
            runtimeType = 'shell';
            executablePath = possiblePath;
            console.log(`[ContextBridge] Found shell executable at: ${possiblePath}`);
            break;
          }
        }
      } else {
        throw new Error(`Unsupported language: ${utilityConfig.language}. Only 'python' and 'shell' are supported.`);
      }

      if (!executablePath) {
        throw new Error(`No executable file found for utility ${utilityId} (expected ${runtimeType} runtime)`);
      }

      console.log(`[ContextBridge] Utility ${utilityId} detected as ${runtimeType} runtime`);

      return {
        utilityId,
        runtimeType,
        executablePath,
        localPath: utility.localPath,
        config: utilityConfig,
        fileData,
        userConfig: config
      };

    } catch (error) {
      console.error('Run utility error:', error);
      throw error;
    }
  }

  /**
   * Uninstall a utility by removing its files and database entry
   */
  async uninstallUtility(utilityId) {
    try {
      const installedUtilities = this.getInstalledUtilities();
      const utility = installedUtilities.find(u => u.id === utilityId);

      if (!utility) {
        throw new Error(`Utility ${utilityId} not found`);
      }

      const utilityDir = path.dirname(utility.localPath);

      // Remove utility directory
      if (fs.existsSync(utilityDir)) {
        fs.rmSync(utilityDir, { recursive: true, force: true });
        console.log(`[ContextBridge] Removed utility directory: ${utilityDir}`);
      }

      // Update installed.json
      const installedData = this.getInstalledData();
      installedData.installed = installedData.installed.filter(u => u.id !== utilityId);
      fs.writeFileSync(this.installedJsonPath, JSON.stringify(installedData, null, 2));

      console.log(`[ContextBridge] Uninstalled utility: ${utilityId}`);
      return { success: true, utilityId };

    } catch (error) {
      console.error('Uninstall utility error:', error);
      throw error;
    }
  }

  // Helper methods
  getInstalledData() {
    try {
      const data = fs.readFileSync(this.installedJsonPath, 'utf8');
      return JSON.parse(data);
    } catch (error) {
      return { installed: [] };
    }
  }

  async downloadFile(url, destinationPath) {
    return new Promise((resolve, reject) => {
      // Handle relative URLs (convert to localhost URL in development)
      if (url.startsWith('/') && !url.startsWith('//')) {
        const isDev = process.env.NODE_ENV === 'development' || !require('electron').app.isPackaged;
        if (isDev) {
          url = `http://localhost:3000${url}`;
        } else {
          // In production, treat as local file path relative to app resources
          const appPath = require('electron').app.getAppPath();
          const localPath = path.join(appPath, 'build', url.substring(1));
          if (fs.existsSync(localPath)) {
            fs.copyFileSync(localPath, destinationPath);
            resolve(destinationPath);
            return;
          } else {
            reject(new Error(`Local file not found: ${localPath}`));
            return;
          }
        }
      }

      // Handle absolute local file paths (for development)
      if (!url.startsWith('http')) {
        if (fs.existsSync(url)) {
          fs.copyFileSync(url, destinationPath);
          resolve(destinationPath);
          return;
        } else {
          reject(new Error(`Local file not found: ${url}`));
          return;
        }
      }

      // Handle HTTP/HTTPS URLs
      const file = fs.createWriteStream(destinationPath);
      const protocol = url.startsWith('https:') ? https : http;

      protocol.get(url, (response) => {
        if (response.statusCode !== 200) {
          reject(new Error(`Failed to download: ${response.statusCode}`));
          return;
        }

        response.pipe(file);

        file.on('finish', () => {
          file.close();
          resolve(destinationPath);
        });

        file.on('error', (err) => {
          fs.unlink(destinationPath, () => {}); // Delete the file on error
          reject(err);
        });
      }).on('error', (err) => {
        reject(err);
      });
    });
  }
}

module.exports = ContextBridge;
