const path = require('path');

module.exports = {
  webpack: {
    configure: (webpackConfig) => {
      // Add fallbacks for Node.js core modules
      webpackConfig.resolve.fallback = {
        ...webpackConfig.resolve.fallback,
        "util": require.resolve("util/"),
        "path": require.resolve("path-browserify"),
        "stream": require.resolve("stream-browserify"),
        "assert": require.resolve("assert/"),
        "constants": require.resolve("constants-browserify"),
        "fs": false,
        "readline": false,
      };

      // Ignore Node.js-specific modules that should only run in main process
      webpackConfig.externals = {
        ...webpackConfig.externals,
        'better-sqlite3': 'commonjs better-sqlite3',
        'electron': 'commonjs electron',
      };

      return webpackConfig;
    },
  },
};
